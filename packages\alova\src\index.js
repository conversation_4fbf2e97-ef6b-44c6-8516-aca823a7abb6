// 动态导入 alova/client 的所有导出
import * as alovaClient from "alova/client";
import { Methods } from "./methods.js";

class Http extends Methods {
  constructor() {
    super();
    // 将 alova/client 的所有方法添加到实例中
    this._attachAlovaClientMethods();

    // 开发环境下输出初始化信息
    if (import.meta.env?.DEV) {
      console.log("%c[Http] @cm/alova HTTP 客户端已初始化 - 包含完整 alova/client 功能", "color: #42b883; font-weight: normal;");
    }
  }

  /**
   * 将 alova/client 的所有方法附加到当前实例
   * @private
   */
  _attachAlovaClientMethods() {
    // 获取 alova/client 的所有导出
    Object.keys(alovaClient).forEach((key) => {
      const exportedItem = alovaClient[key];
      // 如果是函数，绑定到实例上
      if (typeof exportedItem === "function") {
        // 避免覆盖现有的方法
        if (!this[key]) {
          this[key] = exportedItem;
        }
      } else {
        // 如果是其他类型的导出（常量、对象等），也添加到实例上
        if (!this[key]) {
          this[key] = exportedItem;
        }
      }
    });
  }
}

// 导出HTTP请求loading管理功能
export default new Http();
