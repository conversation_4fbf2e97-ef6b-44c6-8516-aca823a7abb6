/**
 * 开发环境日志工具
 *
 * 提供符合ESLint规范的日志记录功能
 * 替换违规的console.group、console.table等方法
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * 日志级别枚举
 */
export const LOG_LEVELS = {
  ERROR: "error",
  WARN: "warn",
  INFO: "info",
  DEBUG: "debug",
};

/**
 * 开发环境日志配置
 */
const LOGGER_CONFIG = {
  enabled: import.meta.env?.DEV || false,
  level: LOG_LEVELS.DEBUG,
  prefix: "[HTTP]",
  enableGrouping: true,
  enableTableOutput: true,
};

/**
 * 统一的绿色样式配置 - 确保整个打印内容都是绿色
 */
const GREEN_STYLE = "color: #42b883; font-weight: normal;";

/**
 * 格式化时间戳
 * @returns {string} 格式化的时间字符串
 */
function formatTimestamp() {
  return new Date().toLocaleString("sv-SE", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
}

/**
 * 创建日志消息前缀
 * @param {string} level - 日志级别
 * @returns {string} 格式化的前缀
 */
function createLogPrefix(level) {
  return `${LOGGER_CONFIG.prefix} [${level.toUpperCase()}] ${formatTimestamp()}`;
}

/**
 * 开发环境日志工具类
 */
class DevLogger {
  /**
   * 错误日志（始终输出）- 整个打印内容显示为绿色
   * @param {string} message - 错误消息
   * @param {...any} args - 附加参数
   */
  error(message, ...args) {
    const fullMessage = `${createLogPrefix(LOG_LEVELS.ERROR)} ${message} ${args.length > 0 ? args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg) : String(arg))).join(" ") : ""}`;
    console.log(`%c${fullMessage}`, GREEN_STYLE);
  }

  /**
   * 警告日志 - 整个打印内容显示为绿色
   * @param {string} message - 警告消息
   * @param {...any} args - 附加参数
   */
  warn(message, ...args) {
    if (LOGGER_CONFIG.enabled) {
      const fullMessage = `${createLogPrefix(LOG_LEVELS.WARN)} ${message} ${args.length > 0 ? args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg) : String(arg))).join(" ") : ""}`;
      console.log(`%c${fullMessage}`, GREEN_STYLE);
    }
  }

  /**
   * 信息日志（仅开发环境）- 整个打印内容显示为绿色
   * @param {string} message - 信息消息
   * @param {...any} args - 附加参数
   */
  info(message, ...args) {
    if (LOGGER_CONFIG.enabled) {
      const fullMessage = `${createLogPrefix(LOG_LEVELS.INFO)} ${message} ${args.length > 0 ? args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg) : String(arg))).join(" ") : ""}`;
      console.log(`%c${fullMessage}`, GREEN_STYLE);
    }
  }

  /**
   * 调试日志（仅开发环境）- 整个打印内容显示为绿色
   * @param {string} message - 调试消息
   * @param {...any} args - 附加参数
   */
  debug(message, ...args) {
    if (LOGGER_CONFIG.enabled && LOGGER_CONFIG.level === LOG_LEVELS.DEBUG) {
      const fullMessage = `${createLogPrefix(LOG_LEVELS.DEBUG)} ${message} ${args.length > 0 ? args.map((arg) => (typeof arg === "object" ? JSON.stringify(arg) : String(arg))).join(" ") : ""}`;
      console.log(`%c${fullMessage}`, GREEN_STYLE);
    }
  }

  /**
   * 分组日志开始（符合ESLint规范的替代方案）
   * @param {string} title - 分组标题
   * @param {string} errorType - 错误类型
   */
  startGroup(title, errorType = "") {
    if (LOGGER_CONFIG.enabled && LOGGER_CONFIG.enableGrouping) {
      const groupTitle = errorType ? `${title} - ${errorType}` : title;
      this.error(`\n=== ${groupTitle} ===`);
    }
  }

  /**
   * 分组日志结束
   */
  endGroup() {
    if (LOGGER_CONFIG.enabled && LOGGER_CONFIG.enableGrouping) {
      this.error("=== End Group ===\n");
    }
  }

  /**
   * 表格式日志输出（符合ESLint规范的替代方案）
   * @param {object} data - 要输出的数据对象
   * @param {string} label - 表格标签
   */
  table(data, label = "数据详情") {
    if (LOGGER_CONFIG.enabled && LOGGER_CONFIG.enableTableOutput) {
      this.error(`${label}:`);

      // 使用JSON格式化输出，模拟表格效果
      if (typeof data === "object" && data !== null) {
        Object.entries(data).forEach(([key, value]) => {
          this.error(`  ${key}: ${JSON.stringify(value)}`);
        });
      } else {
        this.error(`  ${JSON.stringify(data)}`);
      }
    }
  }

  /**
   * 错误上下文详细日志
   * @param {object} errorContext - 错误上下文对象
   * @param {Error} originalError - 原始错误对象
   */
  logErrorContext(errorContext, originalError) {
    if (!LOGGER_CONFIG.enabled) return;

    this.startGroup("HTTP请求错误", errorContext.type);
    this.error("错误详情:", originalError);
    this.table(errorContext, "错误上下文");
    this.endGroup();
  }

  /**
   * 性能日志
   * @param {string} operation - 操作名称
   * @param {number} duration - 执行时长（毫秒）
   * @param {object} metadata - 元数据
   */
  performance(operation, duration, metadata = {}) {
    if (LOGGER_CONFIG.enabled) {
      this.info(`Performance: ${operation} took ${duration}ms`, metadata);
    }
  }

  /**
   * 配置日志工具
   * @param {object} config - 配置选项
   */
  configure(config) {
    Object.assign(LOGGER_CONFIG, config);
  }

  /**
   * 获取当前配置
   * @returns {object} 当前日志配置
   */
  getConfig() {
    return { ...LOGGER_CONFIG };
  }
}

// 创建单例实例
const logger = new DevLogger();

// 导出日志工具实例和配置
export { logger, LOGGER_CONFIG };
export default logger;
