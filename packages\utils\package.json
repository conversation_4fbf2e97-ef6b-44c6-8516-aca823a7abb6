{"name": "@cm/utils", "type": "module", "version": "0.0.1", "description": "通用工具库，包含加密、存储管理、深拷贝和树结构处理功能", "author": "", "license": "MIT", "main": "./src/index.js", "types": "./src/index.d.ts", "exports": {".": {"types": "./src/index.d.ts", "default": "./src/index.js"}, "./crypto": {"types": "./src/index.d.ts", "default": "./src/crypto.js"}, "./storage": {"types": "./src/index.d.ts", "default": "./src/storage.js"}, "./deep-clone": {"types": "./src/index.d.ts", "default": "./src/deep-clone.js"}, "./validators": {"types": "./src/index.d.ts", "default": "./src/validators.js"}, "./dom-utils": {"types": "./src/index.d.ts", "default": "./src/dom-utils.js"}, "./tree-utils": {"types": "./src/index.d.ts", "default": "./src/tree-utils/index.js"}, "./tree-utils/builders": {"types": "./src/index.d.ts", "default": "./src/tree-utils/builders.js"}, "./tree-utils/traversal": {"types": "./src/index.d.ts", "default": "./src/tree-utils/traversal.js"}, "./tree-utils/manipulation": {"types": "./src/index.d.ts", "default": "./src/tree-utils/manipulation.js"}}, "dependencies": {"crypto-js": "^4.2.0", "localforage": "^1.10.0", "mitt": "^3.0.1"}}