import usePermissionStoreHook from "@/store/modules/permission";
import { initRouter } from "../utils";
import { handleLoginExpired } from "./auth";

/**
 * 权限相关路由守卫
 */

/**
 * 初始化动态路由并处理跳转
 * @param {object} to - 目标路由对象
 * @returns {Promise<object|undefined>} 重定向对象或undefined
 */
export async function initDynamicRouter(to) {
  try {
    await initRouter();

    // 如果路由不存在，则返回替换模式的重定向对象
    if (!to.name) {
      return { ...to, replace: true };
    }

    // 路由存在，返回undefined表示继续正常流程
    return undefined;
  } catch (error) {
    console.error("动态路由初始化失败:", error);
    return handleLoginExpired();
  }
}

/**
 * 权限检查守卫
 * @param {object} to - 目标路由对象
 * @param {object} _from - 来源路由对象
 * @returns {Promise<boolean|string|object>} 守卫执行结果
 */
export async function permissionGuard(to, _from) {
  try {
    const permissionStore = usePermissionStoreHook();
    const { VITE_LOCAL_MENU } = import.meta.env;

    // 判断是否需要初始化动态路由
    const needInitRouter = permissionStore.wholeMenus.length === 0 && VITE_LOCAL_MENU === "false";

    // 需要初始化动态路由时，先执行初始化
    if (needInitRouter) {
      const redirectResult = await initDynamicRouter(to);
      if (redirectResult) {
        return redirectResult;
      }
    }

    // 权限检查通过，继续执行后续守卫
    return true;
  } catch (error) {
    return handleLoginExpired();
  }
}

/**
 * 检查用户是否有访问特定路由的权限
 * @param {object} route - 路由对象
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} 是否有权限访问
 */
export function hasRoutePermission(route, userPermissions = []) {
  // 如果路由没有设置权限要求，默认允许访问
  if (!route.meta?.permission) {
    return true;
  }

  // 检查用户是否有对应权限
  const requiredPermission = route.meta.permission;
  return userPermissions.includes(requiredPermission);
}
